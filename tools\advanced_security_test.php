<?php
/*
 * <AUTHOR> 95DIR高级安全系统
 * @Description  : 95DIR分类目录系统高级安全测试工具
 * @Version      : 2.0
 * @Date         : 2025-08-07
 * Copyright (c) 2025 by 95DIR, All Rights Reserved.
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('IN_HANFOX', true);

// 加载必要的文件
require(APP_PATH . 'include/function.php');
require(APP_PATH . 'version.php');
require(APP_PATH . 'include/security_core.php');
require(APP_PATH . 'include/integrity_guard.php');
require(APP_PATH . 'include/license.php');

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>95DIR高级安全测试工具</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .security-badge { background: #28a745; color: #fff; padding: 5px 15px; border-radius: 20px; font-size: 12px; margin-left: 10px; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; margin: 20px 0; }
        .test-box { background: #f8f9fa; padding: 20px; border-radius: 6px; border: 1px solid #dee2e6; }
        .test-item { margin-bottom: 15px; padding: 10px; border-radius: 4px; }
        .test-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .test-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .test-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .test-title { font-weight: bold; margin-bottom: 5px; }
        .test-detail { font-size: 14px; }
        .info-item { margin-bottom: 10px; }
        .info-label { font-weight: bold; color: #333; display: inline-block; width: 120px; }
        .info-value { color: #666; font-family: monospace; }
        .btn { background: #007bff; color: #fff; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                95DIR高级安全测试工具
                <span class="security-badge">安全等级：⭐⭐⭐⭐⭐</span>
            </div>
            <p>全面测试系统的高级安全保护机制</p>
        </div>
        
        <?php
        $tests = array();
        $total_tests = 0;
        $passed_tests = 0;
        
        // 测试1：安全核心模块
        $total_tests++;
        try {
            $security = SecurityCore::getInstance();
            $dynamic_key = $security->generate_dynamic_key('test');
            if (strlen($dynamic_key) === 128) { // SHA512长度
                $tests[] = array('type' => 'success', 'title' => '安全核心模块', 'detail' => '动态密钥生成正常，密钥长度：' . strlen($dynamic_key) . ' 字符');
                $passed_tests++;
            } else {
                $tests[] = array('type' => 'error', 'title' => '安全核心模块', 'detail' => '动态密钥生成异常');
            }
        } catch (Exception $e) {
            $tests[] = array('type' => 'error', 'title' => '安全核心模块', 'detail' => '模块加载失败：' . $e->getMessage());
        }
        
        // 测试2：完整性保护
        $total_tests++;
        try {
            $guard = IntegrityGuard::getInstance();
            $integrity_result = $guard->check_file_integrity();
            if ($integrity_result) {
                $tests[] = array('type' => 'success', 'title' => '文件完整性保护', 'detail' => '所有核心文件完整性验证通过');
                $passed_tests++;
            } else {
                $tests[] = array('type' => 'warning', 'title' => '文件完整性保护', 'detail' => '检测到文件可能被修改');
            }
        } catch (Exception $e) {
            $tests[] = array('type' => 'error', 'title' => '文件完整性保护', 'detail' => '检查失败：' . $e->getMessage());
        }
        
        // 测试3：高级加密测试
        $total_tests++;
        try {
            $security = SecurityCore::getInstance();
            $test_data = 'This is a test string for encryption';
            $encrypted = $security->advanced_encrypt($test_data, 'test');
            $decrypted = $security->advanced_decrypt($encrypted, 'test');
            
            if ($decrypted === $test_data) {
                $tests[] = array('type' => 'success', 'title' => '高级加密系统', 'detail' => '多重加密/解密测试通过，加密后长度：' . strlen($encrypted) . ' 字符');
                $passed_tests++;
            } else {
                $tests[] = array('type' => 'error', 'title' => '高级加密系统', 'detail' => '加密/解密测试失败');
            }
        } catch (Exception $e) {
            $tests[] = array('type' => 'error', 'title' => '高级加密系统', 'detail' => '测试失败：' . $e->getMessage());
        }
        
        // 测试4：授权验证系统
        $total_tests++;
        try {
            $validator = new LicenseValidator();
            $license_file = ROOT_PATH . 'data/license.dat';
            
            if (file_exists($license_file)) {
                $license_valid = $validator->validate();
                if ($license_valid) {
                    $tests[] = array('type' => 'success', 'title' => '授权验证系统', 'detail' => '授权验证通过，系统已获得有效授权');
                    $passed_tests++;
                } else {
                    $tests[] = array('type' => 'warning', 'title' => '授权验证系统', 'detail' => '授权验证失败：' . $validator->get_error_message());
                }
            } else {
                $tests[] = array('type' => 'warning', 'title' => '授权验证系统', 'detail' => '授权文件不存在，系统未授权');
            }
        } catch (Exception $e) {
            $tests[] = array('type' => 'error', 'title' => '授权验证系统', 'detail' => '测试失败：' . $e->getMessage());
        }
        
        // 测试5：反调试机制
        $total_tests++;
        if (function_exists('advanced_security_check')) {
            try {
                $anti_debug_result = advanced_security_check();
                if ($anti_debug_result) {
                    $tests[] = array('type' => 'success', 'title' => '反调试机制', 'detail' => '反调试检查通过，未检测到调试环境');
                    $passed_tests++;
                } else {
                    $tests[] = array('type' => 'warning', 'title' => '反调试机制', 'detail' => '检测到可疑的调试环境');
                }
            } catch (Exception $e) {
                $tests[] = array('type' => 'error', 'title' => '反调试机制', 'detail' => '检查失败：' . $e->getMessage());
            }
        } else {
            $tests[] = array('type' => 'warning', 'title' => '反调试机制', 'detail' => '反调试函数不存在（可能未启用）');
        }
        
        // 测试6：随机验证机制
        $total_tests++;
        if (function_exists('random_security_verify')) {
            $random_tests = 0;
            $random_passed = 0;
            
            // 执行多次随机验证测试
            for ($i = 0; $i < 10; $i++) {
                $random_tests++;
                if (random_security_verify()) {
                    $random_passed++;
                }
            }
            
            if ($random_passed >= 8) { // 至少80%通过
                $tests[] = array('type' => 'success', 'title' => '随机验证机制', 'detail' => "随机验证测试通过率：{$random_passed}/{$random_tests}");
                $passed_tests++;
            } else {
                $tests[] = array('type' => 'warning', 'title' => '随机验证机制', 'detail' => "随机验证通过率较低：{$random_passed}/{$random_tests}");
            }
        } else {
            $tests[] = array('type' => 'error', 'title' => '随机验证机制', 'detail' => '随机验证函数不存在');
        }
        
        // 计算安全评分
        $security_score = round(($passed_tests / $total_tests) * 100, 1);
        $security_level = $security_score >= 95 ? '很难破解' : ($security_score >= 80 ? '较难破解' : ($security_score >= 60 ? '中等难度' : '容易破解'));
        
        ?>
        
        <!-- 安全评分显示 -->
        <div class="test-box" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #fff; text-align: center;">
            <h3>🛡️ 系统安全评分</h3>
            <div style="font-size: 48px; font-weight: bold; margin: 20px 0;"><?php echo $security_score; ?>%</div>
            <div style="font-size: 18px; margin-bottom: 10px;">安全等级：<?php echo $security_level; ?></div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: <?php echo $security_score; ?>%;"></div>
            </div>
            <div style="font-size: 14px;">通过测试：<?php echo $passed_tests; ?>/<?php echo $total_tests; ?></div>
        </div>
        
        <!-- 测试结果显示 -->
        <div class="test-grid">
            <?php foreach ($tests as $test): ?>
            <div class="test-item test-<?php echo $test['type']; ?>">
                <div class="test-title"><?php echo $test['title']; ?></div>
                <div class="test-detail"><?php echo $test['detail']; ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- 系统信息 -->
        <div class="test-box">
            <h4>🔍 系统安全信息</h4>
            <div class="info-item">
                <span class="info-label">当前域名：</span>
                <span class="info-value"><?php echo $_SERVER['HTTP_HOST'] ?? 'unknown'; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">系统版本：</span>
                <span class="info-value"><?php echo defined('SYS_VERSION') ? SYS_VERSION : 'unknown'; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">PHP版本：</span>
                <span class="info-value"><?php echo PHP_VERSION; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">机器码：</span>
                <span class="info-value"><?php 
                    $validator = new LicenseValidator();
                    echo $validator->generate_machine_code();
                ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">系统指纹：</span>
                <span class="info-value"><?php 
                    $guard = IntegrityGuard::getInstance();
                    echo substr($guard->get_system_fingerprint(), 0, 32) . '...';
                ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">动态密钥：</span>
                <span class="info-value"><?php 
                    $security = SecurityCore::getInstance();
                    echo substr($security->generate_dynamic_key('display'), 0, 32) . '...';
                ?></span>
            </div>
        </div>
        
        <!-- 安全建议 -->
        <div class="test-box">
            <h4>💡 安全建议</h4>
            <?php if ($security_score >= 95): ?>
                <div class="test-success">
                    <strong>✅ 安全等级优秀！</strong><br>
                    您的系统具备了很强的安全保护能力，可以有效防止大多数破解尝试。
                </div>
            <?php elseif ($security_score >= 80): ?>
                <div class="test-warning">
                    <strong>⚠️ 安全等级良好</strong><br>
                    系统具备基本的安全保护，建议启用所有保护机制以提高安全性。
                </div>
            <?php else: ?>
                <div class="test-error">
                    <strong>❌ 安全等级不足</strong><br>
                    系统存在安全风险，请立即检查并修复相关问题。
                </div>
            <?php endif; ?>
            
            <div style="margin-top: 15px; font-size: 14px;">
                <h5>提升安全性的建议：</h5>
                <ul>
                    <li>启用生产环境的所有安全检查</li>
                    <li>定期更新授权文件</li>
                    <li>监控系统访问日志</li>
                    <li>使用HTTPS协议</li>
                    <li>定期备份重要数据</li>
                </ul>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="advanced_license_generator.php" class="btn btn-success">生成高级授权</a>
            <a href="license_test.php" class="btn">基础授权测试</a>
            <a href="system_health_check.php" class="btn btn-warning">系统健康检查</a>
            <a href="../index.php" class="btn">返回首页</a>
            <a href="javascript:location.reload()" class="btn">重新测试</a>
        </div>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px;">
            <h4>🔐 高级安全特性说明：</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h5>加密保护</h5>
                    <ul>
                        <li>AES-256-CBC加密</li>
                        <li>自定义RC4流密码</li>
                        <li>混淆编码</li>
                        <li>动态密钥生成</li>
                    </ul>
                </div>
                <div>
                    <h5>验证机制</h5>
                    <ul>
                        <li>域名绑定验证</li>
                        <li>机器码硬件绑定</li>
                        <li>数字签名验证</li>
                        <li>校验和完整性检查</li>
                    </ul>
                </div>
                <div>
                    <h5>反破解保护</h5>
                    <ul>
                        <li>调试器检测</li>
                        <li>虚拟机环境检测</li>
                        <li>代码完整性检查</li>
                        <li>运行时环境验证</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
