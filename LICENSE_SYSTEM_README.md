# 95DIR分类目录系统授权保护系统

## 概述

本授权系统为95DIR分类目录系统提供了多层次的保护机制，包括域名绑定、机器码验证、在线验证、文件完整性检查和反调试机制，有效防止软件被盗版和破解。

## 系统架构

### 第一阶段：基础授权保护
- ✅ 域名绑定验证
- ✅ 授权文件加密存储
- ✅ 到期时间检查
- ✅ 版本授权验证
- ✅ 授权验证失败处理

### 第二阶段：增强保护机制
- ✅ 机器码生成和验证
- ✅ 在线授权验证
- ✅ 核心文件完整性检查
- ✅ 授权管理后台

### 第三阶段：高级保护和管理
- ✅ 反调试和反破解机制
- ✅ 授权服务器端验证
- ✅ 运行环境检测
- ✅ 完整的管理工具

## 文件结构

```
├── source/include/
│   ├── license.php          # 授权验证核心模块
│   └── anti_debug.php       # 反调试和反破解模块
├── system/
│   ├── license_manager.php  # 授权管理后台
│   └── templates/
│       └── license_manager.html  # 管理界面模板
├── tools/
│   ├── license_generator.php     # 授权文件生成工具
│   ├── license_test.php          # 授权测试工具
│   ├── create_demo_license.php   # 演示授权创建工具
│   └── license_server_verify.php # 服务器端验证脚本
└── data/
    └── license.dat          # 授权文件（加密存储）
```

## 使用指南

### 1. 生成授权文件

访问 `tools/license_generator.php` 来生成授权文件：

1. 填写客户域名（支持通配符，如 `*.example.com`）
2. 设置授权到期时间
3. 选择授权版本（可选择 `*` 支持所有版本）
4. 选择授权类型（商业/个人/试用/开发者）
5. 填写客户信息（可选）
6. 输入机器码（可选，用于绑定特定服务器）

生成的授权文件会保存在 `data/` 目录下。

### 2. 部署授权文件

将生成的 `license.dat` 文件发送给客户，客户需要将文件放置在网站根目录的 `data/` 文件夹下。

### 3. 测试授权

使用 `tools/license_test.php` 来测试授权状态：

- 检查授权文件是否存在
- 验证授权信息是否正确
- 显示详细的授权信息
- 提供机器码信息

### 4. 管理后台

在系统管理后台的"授权管理"菜单中可以：

- 查看当前授权状态
- 上传新的授权文件
- 获取机器码信息
- 执行在线验证
- 进行文件完整性检查

## 授权验证流程

### 基础验证流程

1. **文件存在性检查**：检查 `data/license.dat` 文件是否存在
2. **文件解密**：使用密钥解密授权文件
3. **域名验证**：检查当前域名是否与授权域名匹配
4. **到期时间验证**：检查授权是否已过期
5. **版本验证**：检查当前系统版本是否获得授权
6. **机器码验证**：如果设置了机器码，验证是否匹配

### 在线验证流程

1. **本地验证**：首先进行本地授权验证
2. **时间检查**：检查距离上次在线验证是否超过7天
3. **服务器通信**：向授权服务器发送验证请求
4. **签名验证**：服务器验证请求签名的有效性
5. **数据库查询**：服务器查询授权数据库
6. **结果返回**：返回验证结果并更新本地记录

## 安全特性

### 1. 加密保护
- 授权文件使用AES加密存储
- 通信数据使用数字签名验证
- 敏感信息不以明文存储

### 2. 反调试机制
- 检测常见调试器（Xdebug等）
- 检测虚拟机环境
- 检测破解工具特征
- 时间攻击检测

### 3. 完整性保护
- 核心文件MD5校验
- 代码完整性检查
- 运行时环境验证

### 4. 多重验证
- 域名绑定
- 机器码绑定
- 在线验证
- 时间限制

## 配置选项

### 启用/禁用功能

在 `source/init.php` 中可以控制各种保护机制：

```php
// 反调试检查（生产环境建议启用）
if (!anti_debug_check()) {
    exit;
}

// 文件完整性检查（可选，影响性能）
if (!check_file_integrity()) {
    exit('系统文件完整性验证失败！');
}
```

### 在线验证配置

在 `source/include/license.php` 中修改验证服务器地址：

```php
$verify_url = 'https://license.95dir.com/verify.php';
```

## 部署建议

### 开发环境
- 可以禁用反调试机制
- 使用演示授权文件
- 启用详细错误信息

### 生产环境
- 启用所有保护机制
- 使用正式授权文件
- 禁用错误显示
- 定期更新授权文件

### 服务器要求
- PHP 7.0+
- MySQL 5.6+
- cURL 扩展（用于在线验证）
- 文件读写权限

## 故障排除

### 常见问题

1. **授权验证失败**
   - 检查授权文件是否存在
   - 验证域名是否匹配
   - 检查授权是否过期

2. **机器码不匹配**
   - 重新获取机器码
   - 生成新的授权文件

3. **在线验证失败**
   - 检查网络连接
   - 验证服务器地址
   - 检查防火墙设置

### 调试工具

- `tools/license_test.php`：授权状态测试
- `tools/create_demo_license.php`：创建演示授权
- 管理后台授权管理界面

## 技术支持

如需技术支持，请联系：
- 邮箱：<EMAIL>
- 提供域名和机器码信息以获取授权

## 版本历史

- v1.0 (2025-08-07)
  - 基础授权验证功能
  - 域名绑定和时间限制
  - 授权文件生成工具

- v1.1 (2025-08-07)
  - 机器码验证
  - 在线验证机制
  - 管理后台界面

- v1.2 (2025-08-07)
  - 反调试机制
  - 文件完整性检查
  - 服务器端验证

## 许可证

本授权系统版权归95DIR所有，仅供授权用户使用。
