<?php
/**
 * 测试付费审核通知功能
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');

if (file_exists(MOD_PATH.'payment_functions.php')) {
    require_once(MOD_PATH.'payment_functions.php');
}

echo "<h2>测试付费审核通知功能</h2>";

// 获取订单号
$order_no = isset($_GET['order_no']) ? trim($_GET['order_no']) : '202508076566030230404';

echo "<h3>1. 获取订单和网站信息</h3>";
echo "<p>订单号: {$order_no}</p>";

// 获取订单信息
$order_table = $DB->table('payment_orders');
$order = $DB->fetch_one("SELECT * FROM $order_table WHERE order_no = '$order_no'");

if (!$order) {
    echo "<p style='color: red;'>✗ 订单不存在</p>";
    exit;
}

echo "<p style='color: green;'>✓ 订单存在</p>";
echo "<p>订单状态: " . ($order['status'] == 1 ? '已支付' : '待支付') . "</p>";
echo "<p>付费类型: " . $order['payment_type'] . "</p>";
echo "<p>支付金额: ¥" . $order['amount'] . "</p>";

// 获取网站信息
if ($order['web_id'] > 0) {
    $websites_table = $DB->table('websites');
    $website = $DB->fetch_one("SELECT * FROM $websites_table WHERE web_id = " . $order['web_id']);
    
    if ($website) {
        echo "<p style='color: green;'>✓ 网站记录存在</p>";
        echo "<p>网站ID: " . $website['web_id'] . "</p>";
        echo "<p>网站名称: " . htmlspecialchars($website['web_name']) . "</p>";
        echo "<p>网站地址: " . htmlspecialchars($website['web_url']) . "</p>";
        
        echo "<h3>2. 测试通知功能</h3>";
        
        // 检查通知配置
        echo "<h4>通知配置检查：</h4>";
        echo "<p>SMTP主机: " . ($options['smtp_host'] ? '✓ 已配置' : '✗ 未配置') . "</p>";
        echo "<p>SMTP用户: " . ($options['smtp_user'] ? '✓ 已配置' : '✗ 未配置') . "</p>";
        echo "<p>管理员邮箱: " . ($options['admin_email'] ? '✓ 已配置 (' . $options['admin_email'] . ')' : '✗ 未配置') . "</p>";
        echo "<p>微信机器人: " . ($options['wechat_robot'] ? '✓ 已配置' : '✗ 未配置') . "</p>";
        echo "<p>钉钉机器人: " . ($options['dingding_robot'] ? '✓ 已配置' : '✗ 未配置') . "</p>";
        echo "<p>用户邮箱: " . ($order['web_email'] ? '✓ 已配置 (' . $order['web_email'] . ')' : '✗ 未配置') . "</p>";
        
        if (isset($_GET['send_test']) && $_GET['send_test'] == '1') {
            echo "<h4>发送测试通知：</h4>";
            
            if (function_exists('send_payment_approval_notifications')) {
                try {
                    send_payment_approval_notifications($website, $order);
                    echo "<p style='color: green;'>✓ 通知发送完成</p>";
                    
                    // 检查错误日志
                    $error_logs = array(
                        'payment_email_error.log' => '邮件错误',
                        'payment_wechat_error.log' => '微信错误',
                        'payment_dingding_error.log' => '钉钉错误'
                    );
                    
                    foreach ($error_logs as $log_file => $log_name) {
                        $log_path = ROOT_PATH . "data/" . $log_file;
                        if (file_exists($log_path)) {
                            $log_content = file_get_contents($log_path);
                            if (!empty($log_content)) {
                                echo "<p style='color: orange;'>⚠ {$log_name}日志有内容，请检查</p>";
                                echo "<pre style='background: #f5f5f5; padding: 10px; font-size: 12px;'>" . htmlspecialchars(substr($log_content, -500)) . "</pre>";
                            }
                        }
                    }
                    
                } catch (Exception $e) {
                    echo "<p style='color: red;'>✗ 通知发送失败: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p style='color: red;'>✗ send_payment_approval_notifications 函数不存在</p>";
            }
        } else {
            echo "<p><a href='?order_no={$order_no}&send_test=1' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>🚀 发送测试通知</a></p>";
        }
        
        echo "<h3>3. 通知内容预览</h3>";
        
        // 显示不同付费类型的通知内容
        $payment_types = array(
            1 => array('name' => 'VIP收录', 'icon' => '👑', 'color' => '#6f42c1'),
            2 => array('name' => '推荐收录', 'icon' => '🔥', 'color' => '#dc3545'),
            3 => array('name' => '快审收录', 'icon' => '⚡', 'color' => '#ffc107')
        );
        
        $payment_info = isset($payment_types[$order['payment_type']]) ? $payment_types[$order['payment_type']] : array('name' => '付费收录', 'icon' => '💰', 'color' => '#007bff');
        
        echo "<h4>邮件主题预览：</h4>";
        echo "<p>[" . $options['site_name'] . "] " . $payment_info['icon'] . " " . $payment_info['name'] . "审核通过！</p>";
        
        echo "<h4>微信/钉钉通知内容预览：</h4>";
        echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>";
        echo $payment_info['icon'] . " " . $payment_info['name'] . "自动审核完成\n";
        echo "站点名称：" . $website['web_name'] . "\n";
        echo "站点地址：" . $website['web_url'] . "\n";
        echo "支付金额：¥" . $order['amount'] . "\n";
        echo "审核时间：" . date('Y-m-d H:i:s') . "\n";
        echo "网站ID：" . $website['web_id'];
        echo "</pre>";
        
    } else {
        echo "<p style='color: red;'>✗ 网站记录不存在</p>";
    }
} else {
    echo "<p style='color: red;'>✗ 订单未关联网站记录</p>";
}

echo "<hr>";
echo "<p><a href='check_payment_status.php?order_no={$order_no}'>检查支付状态</a></p>";
echo "<p><a href='?mod=payment&order_no={$order_no}'>返回支付页面</a></p>";
?>
